{"main": {"id": "e9ac41c5ed21f187", "type": "split", "children": [{"id": "1d13ee0151c53a57", "type": "tabs", "children": [{"id": "0b1d566cffc5123b", "type": "leaf", "state": {"type": "empty", "state": {}, "icon": "lucide-file", "title": "New tab"}}]}], "direction": "vertical"}, "left": {"id": "d2b4f994ab6f37b0", "type": "split", "children": [{"id": "779b046fa5b69029", "type": "tabs", "children": [{"id": "d5bc9ec40b2bcf22", "type": "leaf", "state": {"type": "file-explorer", "state": {"sortOrder": "alphabetical", "autoReveal": false}, "icon": "lucide-folder-closed", "title": "Files"}}, {"id": "9c30fb2bb2bc7a0b", "type": "leaf", "state": {"type": "search", "state": {"query": "", "matchingCase": false, "explainSearch": false, "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical"}, "icon": "lucide-search", "title": "Search"}}, {"id": "1decc83a0b516cb8", "type": "leaf", "state": {"type": "bookmarks", "state": {}, "icon": "lucide-bookmark", "title": "Bookmarks"}}]}], "direction": "horizontal", "width": 300}, "right": {"id": "ca3b7ea4be1373be", "type": "split", "children": [{"id": "f978ae2461e281f2", "type": "tabs", "children": [{"id": "8af248df1e2fcf05", "type": "leaf", "state": {"type": "backlink", "state": {"collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-coming-in", "title": "Backlinks"}}, {"id": "5fcb0bcc4051bbd1", "type": "leaf", "state": {"type": "outgoing-link", "state": {"linksCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-going-out", "title": "Outgoing links"}}, {"id": "b9b23f8bd473e83e", "type": "leaf", "state": {"type": "tag", "state": {"sortOrder": "frequency", "useHierarchy": true, "showSearch": false, "searchQuery": ""}, "icon": "lucide-tags", "title": "Tags"}}, {"id": "9a15c6a0365488f3", "type": "leaf", "state": {"type": "outline", "state": {"followCursor": false, "showSearch": false, "searchQuery": ""}, "icon": "lucide-list", "title": "Outline"}}]}], "direction": "horizontal", "width": 300, "collapsed": true}, "left-ribbon": {"hiddenItems": {"switcher:Open quick switcher": false, "graph:Open graph view": false, "canvas:Create new canvas": false, "daily-notes:Open today's daily note": false, "templates:Insert template": false, "command-palette:Open command palette": false}}, "active": "0b1d566cffc5123b", "lastOpenFiles": ["obj/Debug/net8.0/WorkerExtensions/buildout/bin/System.Net.Http.Formatting.dll", "obj/Debug/net8.0/WorkerExtensions/buildout/bin/System.Memory.Data.dll", "obj/Debug/net8.0/WorkerExtensions/buildout/bin/Newtonsoft.Json.dll", "obj/Debug/net8.0/WorkerExtensions/buildout/bin/Newtonsoft.Json.Bson.dll", "obj/Debug/net8.0/WorkerExtensions/buildout/bin/Microsoft.Extensions.Logging.Abstractions.dll", "obj/Debug/net8.0/WorkerExtensions/buildout/bin/Microsoft.Extensions.Hosting.dll", "obj/Debug/net8.0/WorkerExtensions/buildout/bin/Microsoft.Extensions.Hosting.Abstractions.dll", "obj/Debug/net8.0/WorkerExtensions/buildout/bin/Microsoft.Extensions.FileSystemGlobbing.dll", "obj/Debug/net8.0/WorkerExtensions/buildout/bin/Microsoft.Extensions.FileProviders.Physical.dll", "obj/Debug/net8.0/WorkerExtensions/buildout/bin/Microsoft.Extensions.FileProviders.Abstractions.dll", "obj/Debug/net8.0/WorkerExtensions/buildout/bin/Microsoft.Extensions.DependencyModel.dll", "docs/Account Retention System Technical Documentation.md", "docs/IMPLEMENTATION/Documentation Update Summary - Configuration Simplification.md", "docs/ASSESSMENTS/Configuration Simplification Impact Analysis.md", "docs/IMPLEMENTATION/Simplified Configuration Validation.md", "docs/QUICK_FIXES/Placeholder Cleanup Guide.md", "docs/ASSESSMENTS/Configuration Complexity and Placeholder Cleanup Analysis.md", "docs/IMPLEMENTATION/Environment-Aware Configuration Implementation.md", "docs/RECOMMENDATIONS/Azure Functions Key Vault Strategy Analysis.md", "docs/IMPLEMENTATION/Key Vault Configuration Logic.md", "docs/TESTING/Configuration Validation Test Guide.md", "docs/IMPLEMENTATION/Configuration Standardization Implementation.md", "docs/ASSESSMENTS/Error Handling and Debug Logging Analysis.md", "docs/Current Architecture Summary.md", "docs/AuthenticationFunction Removal.md", "docs/Testing Strategy.md", "docs/ASSESSMENTS/Entra External ID Native Password Policy Capabilities Analysis.md", "docs/ARCHIVE/ARCHIVE_NOTE.md", "docs/ASSESSMENTS/ARCHIVE_NOTE.md", "docs/Configuration Variables Reference.md", "docs/ASSESSMENTS/PowerPagesCustomAuth Password Policy Comprehensive Analysis.md", "docs/Entra External ID Native Password Policy Capabilities Analysis.md", "docs/Azure Functions Authentication System Application Separation Analysis - August 14th 2024.md", "docs/ASSESSMENTS/Azure Functions Authentication System Application Separation Analysis - August 14th 2024.md", "docs/TODO/CORS Solution.md", "docs/Production Deployment Setup Guide.md"]}